import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/theme.dart';

enum CardVariant {
  elevated,
  outlined,
  filled,
  gradient,
}

class EnhancedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final CardVariant variant;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final LinearGradient? gradient;
  final List<BoxShadow>? boxShadow;
  final bool isInteractive;
  final bool showRipple;
  final Duration animationDuration;

  const EnhancedCard({
    super.key,
    required this.child,
    this.onTap,
    this.variant = CardVariant.elevated,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.gradient,
    this.boxShadow,
    this.isInteractive = true,
    this.showRipple = true,
    this.animationDuration = const Duration(milliseconds: 200),
  });

  const EnhancedCard.elevated({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.isInteractive = true,
    this.showRipple = true,
    this.animationDuration = const Duration(milliseconds: 200),
  })  : variant = CardVariant.elevated,
        gradient = null,
        boxShadow = null;

  const EnhancedCard.outlined({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.isInteractive = true,
    this.showRipple = true,
    this.animationDuration = const Duration(milliseconds: 200),
  })  : variant = CardVariant.outlined,
        gradient = null,
        boxShadow = null;

  const EnhancedCard.gradient({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.borderRadius,
    required this.gradient,
    this.isInteractive = true,
    this.showRipple = true,
    this.animationDuration = const Duration(milliseconds: 200),
  })  : variant = CardVariant.gradient,
        backgroundColor = null,
        boxShadow = null;

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _elevationAnimation = Tween<double>(
      begin: 1.0,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onTap != null && widget.isInteractive) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _resetPress();
  }

  void _handleTapCancel() {
    _resetPress();
  }

  void _resetPress() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  Color _getBackgroundColor() {
    if (widget.backgroundColor != null) {
      return widget.backgroundColor!;
    }

    switch (widget.variant) {
      case CardVariant.elevated:
      case CardVariant.gradient:
        return AppTheme.cardLight;
      case CardVariant.outlined:
        return Colors.transparent;
      case CardVariant.filled:
        return AppTheme.surfaceLight;
    }
  }

  List<BoxShadow> _getBoxShadow() {
    if (widget.boxShadow != null) {
      return widget.boxShadow!;
    }

    switch (widget.variant) {
      case CardVariant.elevated:
        return AppTheme.cardShadow;
      case CardVariant.gradient:
        return AppTheme.elevatedShadow;
      case CardVariant.outlined:
      case CardVariant.filled:
        return [];
    }
  }

  Border? _getBorder() {
    switch (widget.variant) {
      case CardVariant.outlined:
        return Border.all(
          color: AppTheme.borderLight,
          width: 1,
        );
      default:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius = widget.borderRadius ?? AppTheme.largeRadius;
    final padding = widget.padding ?? const EdgeInsets.all(AppTheme.spaceMD);
    final margin = widget.margin ?? const EdgeInsets.all(AppTheme.spaceXS);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: margin,
            child: Material(
              color: Colors.transparent,
              borderRadius: borderRadius,
              child: InkWell(
                onTap: widget.onTap,
                onTapDown: _handleTapDown,
                onTapUp: _handleTapUp,
                onTapCancel: _handleTapCancel,
                borderRadius: borderRadius,
                splashColor: widget.showRipple
                    ? AppTheme.primaryLight.withValues(alpha: 0.1)
                    : Colors.transparent,
                highlightColor: widget.showRipple
                    ? AppTheme.primaryLight.withValues(alpha: 0.05)
                    : Colors.transparent,
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.variant != CardVariant.gradient
                        ? _getBackgroundColor()
                        : null,
                    gradient: widget.variant == CardVariant.gradient
                        ? (widget.gradient ?? AppTheme.cardGradient)
                        : null,
                    borderRadius: borderRadius,
                    border: _getBorder(),
                    boxShadow: _getBoxShadow()
                        .map((shadow) => BoxShadow(
                              color: shadow.color,
                              blurRadius: shadow.blurRadius * _elevationAnimation.value,
                              offset: shadow.offset * _elevationAnimation.value,
                              spreadRadius: shadow.spreadRadius,
                            ))
                        .toList(),
                  ),
                  child: Padding(
                    padding: padding,
                    child: widget.child,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Specialized card components
class StatsCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color? iconColor;
  final LinearGradient? gradient;
  final VoidCallback? onTap;

  const StatsCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    this.iconColor,
    this.gradient,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard.gradient(
      gradient: gradient ?? AppTheme.primaryGradient,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: AppTheme.smallRadius,
                ),
                child: Icon(
                  icon,
                  color: iconColor ?? Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spaceMD),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.w700,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: AppTheme.spaceXS),
            Text(
              subtitle!,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }
}
