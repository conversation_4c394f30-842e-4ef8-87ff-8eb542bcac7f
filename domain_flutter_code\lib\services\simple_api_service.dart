import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../utils/constants.dart';

class SimpleApiService {
  static final SimpleApiService _instance = SimpleApiService._internal();
  factory SimpleApiService() => _instance;
  SimpleApiService._internal();

  // Cache for network status
  bool? _lastNetworkStatus;
  DateTime? _lastNetworkCheck;

  /// Simplified network connectivity check - more practical approach
  Future<bool> _hasNetworkConnection() async {
    try {
      // Use cached result if recent (within 5 seconds)
      if (_lastNetworkCheck != null &&
          _lastNetworkStatus != null &&
          DateTime.now().difference(_lastNetworkCheck!).inSeconds < 5) {
        return _lastNetworkStatus!;
      }

      // Check basic connectivity first
      final connectivityResults = await Connectivity().checkConnectivity();
      final hasBasicConnectivity = connectivityResults.any(
        (result) => result != ConnectivityResult.none,
      );

      if (!hasBasicConnectivity) {
        _lastNetworkStatus = false;
        _lastNetworkCheck = DateTime.now();
        return false;
      }

      // For most cases, if we have basic connectivity, assume we have internet
      // This prevents false negatives in corporate networks, VPNs, etc.
      _lastNetworkStatus = true;
      _lastNetworkCheck = DateTime.now();

      if (kDebugMode) {
        print(
          'Network connectivity: Basic connectivity detected, assuming internet access',
        );
      }

      return true;
    } catch (e) {
      if (kDebugMode) print('Connectivity check error: $e');
      // If check fails, assume connected to avoid blocking the user
      return true;
    }
  }

  /// Get headers with authentication
  Future<Map<String, String>> _getHeaders({bool requiresAuth = true}) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (requiresAuth) {
      try {
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString(StorageKeys.authToken);
        if (token != null && token.isNotEmpty) {
          headers['Authorization'] = 'Bearer $token';
        }
      } catch (e) {
        if (kDebugMode) print('Error getting auth token: $e');
      }
    }

    return headers;
  }

  /// Handle HTTP response
  Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        if (response.body.isNotEmpty) {
          return json.decode(response.body);
        } else {
          return {'success': true, 'data': null};
        }
      } catch (e) {
        throw Exception('Invalid JSON response from server');
      }
    } else {
      String errorMessage;
      try {
        final errorData = json.decode(response.body);
        errorMessage = errorData['message'] ?? 'Server error occurred';
      } catch (e) {
        errorMessage = 'HTTP ${response.statusCode}: ${response.reasonPhrase}';
      }
      throw Exception(errorMessage);
    }
  }

  /// Convert exceptions to user-friendly messages
  String _getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Be more specific about different error types
    if (errorString.contains('socketexception')) {
      if (errorString.contains('network is unreachable')) {
        return 'Network is unreachable. Please check your WiFi or mobile data connection.';
      } else if (errorString.contains('no route to host')) {
        return 'Cannot reach the server. Please check your internet connection.';
      } else {
        return 'Network connection error. Please check your internet and try again.';
      }
    } else if (errorString.contains('timeoutexception') ||
        errorString.contains('timeout')) {
      return 'Request timed out. Please check your connection and try again.';
    } else if (errorString.contains('connection refused')) {
      return 'Server refused connection. Please try again later.';
    } else if (errorString.contains('failed to connect')) {
      return 'Failed to connect to server. Please check your internet connection.';
    } else if (errorString.contains('certificate') ||
        errorString.contains('handshake')) {
      return 'SSL certificate error. Please try again.';
    } else if (errorString.contains('formatexception') ||
        errorString.contains('format')) {
      return 'Invalid server response. Please try again.';
    } else if (errorString.contains('404') ||
        errorString.contains('not found')) {
      return 'Service not found. Please contact support.';
    } else if (errorString.contains('500') ||
        errorString.contains('internal server error')) {
      return 'Server error. Please try again later.';
    } else if (errorString.contains('403') ||
        errorString.contains('forbidden')) {
      return 'Access denied. Please check your permissions.';
    } else if (errorString.contains('401') ||
        errorString.contains('unauthorized')) {
      return 'Session expired. Please login again.';
    } else {
      // Extract the actual error message if it's wrapped
      String message = error.toString();
      if (message.startsWith('Exception: ')) {
        message = message.substring(11);
      }
      return message;
    }
  }

  /// Make HTTP request with simplified error handling
  Future<Map<String, dynamic>> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? queryParameters,
    bool requiresAuth = true,
    int retryCount = 0,
  }) async {
    const maxRetries = 2; // Reduced retries for faster response

    try {
      // Build URI - simplified approach
      final uri = Uri.parse('${ApiConstants.baseUrl}$endpoint');
      final uriWithQuery = queryParameters != null
          ? uri.replace(queryParameters: queryParameters)
          : uri;

      final headers = await _getHeaders(requiresAuth: requiresAuth);

      // Make HTTP request - simplified approach
      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http
              .get(uriWithQuery, headers: headers)
              .timeout(const Duration(seconds: 30));
          break;
        case 'POST':
          response = await http
              .post(
                uriWithQuery,
                headers: headers,
                body: data != null ? json.encode(data) : null,
              )
              .timeout(const Duration(seconds: 30));
          break;
        case 'PUT':
          response = await http
              .put(
                uriWithQuery,
                headers: headers,
                body: data != null ? json.encode(data) : null,
              )
              .timeout(const Duration(seconds: 30));
          break;
        case 'DELETE':
          response = await http
              .delete(uriWithQuery, headers: headers)
              .timeout(const Duration(seconds: 30));
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      if (kDebugMode) {
        print('Response status: ${response.statusCode}');
        print('Response headers: ${response.headers}');
        print('Response body length: ${response.body.length}');
        if (response.body.length < 1000) {
          print('Response body: ${response.body}');
        }
      }

      return _handleResponse(response);
    } catch (e) {
      // Simple retry logic for network errors only
      if (retryCount < maxRetries && _shouldRetry(e)) {
        await Future.delayed(const Duration(seconds: 2));

        return _makeRequest(
          method,
          endpoint,
          data: data,
          queryParameters: queryParameters,
          requiresAuth: requiresAuth,
          retryCount: retryCount + 1,
        );
      }

      // Throw the original error with better message
      final errorMessage = _getErrorMessage(e);
      throw Exception(errorMessage);
    }
  }

  /// Determine if request should be retried based on error type
  bool _shouldRetry(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Network-related errors that should be retried
    return errorString.contains('timeout') ||
        errorString.contains('socketexception') ||
        errorString.contains('connection refused') ||
        errorString.contains('network is unreachable') ||
        errorString.contains('connection reset') ||
        errorString.contains('connection aborted') ||
        errorString.contains('no route to host') ||
        errorString.contains('connection timed out') ||
        errorString.contains('temporary failure in name resolution') ||
        errorString.contains('failed to connect') ||
        errorString.contains('connection closed') ||
        errorString.contains('broken pipe');
  }

  /// Clear network status cache to force fresh connectivity check
  void clearNetworkCache() {
    _lastNetworkStatus = null;
    _lastNetworkCheck = null;
  }

  // API Methods

  /// Simplified API connectivity test
  Future<bool> testConnection() async {
    try {
      if (kDebugMode) {
        print('Testing connection with base URL: ${ApiConstants.baseUrl}');
      }

      // Test basic network connectivity first
      final hasConnection = await _hasNetworkConnection();
      if (kDebugMode) {
        print('Network connectivity: $hasConnection');
      }

      if (!hasConnection) {
        return false;
      }

      // Try a simple request to the API
      try {
        final response = await http
            .get(
              Uri.parse('${ApiConstants.baseUrl}${ApiConstants.loginEndpoint}'),
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              },
            )
            .timeout(const Duration(seconds: 10));

        if (kDebugMode) {
          print('API test status: ${response.statusCode}');
        }

        // Accept any response that indicates server is reachable
        // Even 405 (Method Not Allowed) means the server is responding
        return response.statusCode >= 200 && response.statusCode < 500;
      } catch (e) {
        if (kDebugMode) {
          print('API test failed: $e');
        }

        // If API test fails but we have network, assume API is reachable
        // This prevents false negatives due to CORS, rate limiting, etc.
        return hasConnection;
      }
    } catch (e) {
      if (kDebugMode) print('Connection test failed: $e');
      // If all fails, assume connection is available to avoid blocking user
      return true;
    }
  }

  Future<Map<String, dynamic>> login(String email, String password) async {
    if (kDebugMode) {
      print('=== API SERVICE LOGIN DEBUG ===');
      print('SimpleApiService: login() called');
      print('Email: $email');
      print('Password length: ${password.length}');
      print('Base URL: ${ApiConstants.baseUrl}');
      print('Login Endpoint: ${ApiConstants.loginEndpoint}');
      print('Full URL: ${ApiConstants.baseUrl}${ApiConstants.loginEndpoint}');
    }

    // Use the correct API endpoint that we know works
    try {
      final result = await _makeRequest(
        'POST',
        ApiConstants.loginEndpoint, // This is '/api/auth/login'
        data: {'email': email.trim(), 'password': password},
        requiresAuth: false,
      );

      if (kDebugMode) {
        print('SUCCESS! Login worked');
        print('Result: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Login failed: $e');
        print('Error type: ${e.runtimeType}');
      }
      rethrow;
    }
  }

  Future<Map<String, dynamic>> logout() async {
    return await _makeRequest('POST', ApiConstants.logoutEndpoint);
  }

  Future<Map<String, dynamic>> getUser() async {
    return await _makeRequest('GET', ApiConstants.userEndpoint);
  }

  Future<Map<String, dynamic>> getDomains({
    int page = 1,
    String? search,
    int? categoryId,
    int? rating,
    List<String>? extensions,
  }) async {
    final queryParams = <String, String>{'page': page.toString()};

    if (search != null && search.isNotEmpty) queryParams['search'] = search;
    if (categoryId != null) queryParams['category_id'] = categoryId.toString();
    if (rating != null) queryParams['rating'] = rating.toString();
    if (extensions != null && extensions.isNotEmpty) {
      queryParams['extensions'] = extensions.join(',');
    }

    return await _makeRequest(
      'GET',
      ApiConstants.domainsEndpoint,
      queryParameters: queryParams,
    );
  }

  Future<Map<String, dynamic>> getDashboardStats() async {
    return await _makeRequest('GET', ApiConstants.dashboardStatsEndpoint);
  }

  Future<Map<String, dynamic>> getCategories() async {
    return await _makeRequest('GET', ApiConstants.categoriesEndpoint);
  }
}
