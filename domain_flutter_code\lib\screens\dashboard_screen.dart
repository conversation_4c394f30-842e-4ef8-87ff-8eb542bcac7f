import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../providers/auth_provider.dart';
import '../providers/domain_provider.dart';
import '../utils/theme.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/enhanced_loading.dart';
import '../widgets/empty_state.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Defer loading until after build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final domainProvider = Provider.of<DomainProvider>(context, listen: false);
    await domainProvider.loadDashboardStats();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<DomainProvider>(
        builder: (context, domainProvider, child) {
          if (domainProvider.isLoading) {
            return const Center(
              child: EnhancedLoading.circular(message: 'Loading dashboard...'),
            );
          }

          if (domainProvider.error != null) {
            return EmptyState.error(
              title: 'Dashboard Error',
              description: domainProvider.error!,
              actionText: 'Retry',
              onAction: _loadData,
            );
          }

          final stats = domainProvider.dashboardStats;

          return RefreshIndicator(
            onRefresh: _loadData,
            child: LayoutBuilder(
              builder: (context, constraints) {
                final isSmallScreen = constraints.maxHeight < 600;
                return SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.all(
                    isSmallScreen ? AppTheme.spaceSM : AppTheme.spaceMD,
                  ),
                  child: AnimationLimiter(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: AnimationConfiguration.toStaggeredList(
                        duration: const Duration(milliseconds: 375),
                        childAnimationBuilder: (widget) => SlideAnimation(
                          horizontalOffset: 50.0,
                          child: FadeInAnimation(child: widget),
                        ),
                        children: [
                          // Welcome Header
                          _buildWelcomeHeader(),
                          const SizedBox(height: AppTheme.spaceXL),

                          // Quick Stats Section
                          _buildQuickStats(stats),
                          const SizedBox(height: AppTheme.spaceXL),

                          // Recent Activity Section
                          _buildRecentActivity(),
                          const SizedBox(height: AppTheme.spaceXL),

                          // Quick Actions Section
                          _buildQuickActions(),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return EnhancedCard.gradient(
          gradient: AppTheme.primaryGradient,
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    authProvider.admin?.name.substring(0, 1).toUpperCase() ??
                        'A',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spaceMD),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back!',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spaceXS),
                    Text(
                      authProvider.admin?.name ?? 'Admin',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spaceXS),
                    Text(
                      'Here\'s your domain portfolio overview',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(AppTheme.spaceSM),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: AppTheme.smallRadius,
                ),
                child: const Icon(
                  Icons.dashboard_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickStats(Map<String, dynamic>? stats) {
    if (stats == null) {
      return EnhancedCard.outlined(
        child: const Padding(
          padding: EdgeInsets.all(AppTheme.spaceXL),
          child: Center(
            child: Text(
              'No statistics available',
              style: TextStyle(
                color: AppTheme.textSecondaryLight,
                fontSize: 16,
              ),
            ),
          ),
        ),
      );
    }

    final statItems = [
      _StatItem(
        title: 'Total Domains',
        value: stats['total_domains']?.toString() ?? '0',
        icon: Icons.domain_rounded,
        gradient: AppTheme.primaryGradient,
      ),
      _StatItem(
        title: 'Simple Domains',
        value: stats['total_simple_domains']?.toString() ?? '0',
        icon: Icons.bookmark_rounded,
        gradient: AppTheme.successGradient,
      ),
      _StatItem(
        title: 'Categories',
        value: stats['total_categories']?.toString() ?? '0',
        icon: Icons.category_rounded,
        gradient: AppTheme.secondaryGradient,
      ),
      _StatItem(
        title: 'Expiring Soon',
        value: stats['expiring_soon']?.toString() ?? '0',
        icon: Icons.warning_rounded,
        gradient: AppTheme.warningGradient,
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Statistics',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryLight,
          ),
        ),
        const SizedBox(height: AppTheme.spaceMD),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: AppTheme.spaceMD,
            mainAxisSpacing: AppTheme.spaceMD,
            childAspectRatio: 1.2,
          ),
          itemCount: statItems.length,
          itemBuilder: (context, index) {
            final item = statItems[index];
            return StatsCard(
                  title: item.title,
                  value: item.value,
                  icon: item.icon,
                  gradient: item.gradient,
                )
                .animate(delay: (index * 100).ms)
                .fadeIn()
                .slideY(begin: 0.3, end: 0);
          },
        ),
      ],
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryLight,
          ),
        ),
        const SizedBox(height: AppTheme.spaceMD),
        EnhancedCard.outlined(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spaceMD),
            child: Column(
              children: [
                _buildActivityItem(
                  'Domain Added',
                  'example.com was added to your portfolio',
                  Icons.add_circle_outline,
                  AppTheme.successLight,
                  '2 hours ago',
                ),
                const Divider(),
                _buildActivityItem(
                  'Domain Expiring',
                  'test.com expires in 30 days',
                  Icons.warning_outlined,
                  AppTheme.warningLight,
                  '1 day ago',
                ),
                const Divider(),
                _buildActivityItem(
                  'Category Created',
                  'New category "E-commerce" was created',
                  Icons.category_outlined,
                  AppTheme.infoLight,
                  '3 days ago',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(
    String title,
    String description,
    IconData icon,
    Color color,
    String time,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spaceSM),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spaceSM),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: AppTheme.smallRadius,
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: AppTheme.spaceMD),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryLight,
                  ),
                ),
                const SizedBox(height: AppTheme.spaceXS),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryLight,
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: const TextStyle(
              fontSize: 11,
              color: AppTheme.textTertiaryLight,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryLight,
          ),
        ),
        const SizedBox(height: AppTheme.spaceMD),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Add Domain',
                'Register a new domain',
                Icons.add_rounded,
                AppTheme.primaryGradient,
                () {
                  // Navigate to add domain screen
                },
              ),
            ),
            const SizedBox(width: AppTheme.spaceMD),
            Expanded(
              child: _buildActionCard(
                'Add Category',
                'Create new category',
                Icons.category_rounded,
                AppTheme.secondaryGradient,
                () {
                  // Navigate to add category screen
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return EnhancedCard.gradient(
      gradient: gradient,
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spaceMD),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: AppTheme.smallRadius,
            ),
            child: Icon(icon, color: Colors.white, size: 32),
          ),
          const SizedBox(height: AppTheme.spaceMD),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppTheme.spaceXS),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _StatItem {
  final String title;
  final String value;
  final IconData icon;
  final LinearGradient gradient;

  const _StatItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.gradient,
  });
}
