import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/domain.dart';
import '../providers/category_provider.dart';
import 'package:intl/intl.dart';

class DomainCard extends StatelessWidget {
  final Domain domain;
  final VoidCallback? onDelete;

  const DomainCard({
    super.key,
    required this.domain,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    domain.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (onDelete != null)
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: onDelete,
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: domain.extensions.map((ext) {
                return Chip(
                  label: Text(ext),
                  backgroundColor: Colors.blue.withValues(alpha: 0.1),
                );
              }).toList(),
            ),
            const SizedBox(height: 8),
            Consumer<CategoryProvider>(
              builder: (context, categoryProvider, child) {
                final categories = categoryProvider.getCategoriesByIds(domain.categories);
                return Wrap(
                  spacing: 4,
                  children: categories.map((category) {
                    return Chip(
                      label: Text(category.name),
                      backgroundColor: Color(int.parse(category.color.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.2),
                    );
                  }).toList(),
                );
              },
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 4),
                Text('Expires: ${DateFormat('MMM dd, yyyy').format(DateTime.parse(domain.expiryDate))}'),
                const Spacer(),
                if (domain.daysLeft != null) ...[
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: _getExpiryColor(domain.daysLeft!),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${domain.daysLeft} days left',
                    style: TextStyle(
                      color: _getExpiryColor(domain.daysLeft!),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Row(
                  children: List.generate(5, (index) {
                    return Icon(
                      Icons.star,
                      size: 16,
                      color: index < domain.rating ? Colors.amber : Colors.grey,
                    );
                  }),
                ),
                const Spacer(),
                if (domain.isAvailable != null) ...[
                  Icon(
                    domain.isAvailable! ? Icons.check_circle : Icons.cancel,
                    size: 16,
                    color: domain.isAvailable! ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    domain.isAvailable! ? 'Available' : 'Not Available',
                    style: TextStyle(
                      color: domain.isAvailable! ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getExpiryColor(int daysLeft) {
    if (daysLeft <= 7) return Colors.red;
    if (daysLeft <= 30) return Colors.orange;
    if (daysLeft <= 90) return Colors.yellow[700]!;
    return Colors.green;
  }
}