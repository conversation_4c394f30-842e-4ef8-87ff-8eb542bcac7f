@extends('layouts.app')

@section('title', 'View Reserve Domain - Domain CRM')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-eye me-2 text-info"></i>
        Reserve Domain Details
    </h1>
    <div class="d-flex gap-2">
        <a href="{{ route('simple-domains.edit', $simpleDomain) }}" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i>
            Edit
        </a>
        <a href="{{ route('simple-domains.buy', $simpleDomain) }}" class="btn btn-buy-domain">
            <i class="fas fa-shopping-cart me-1"></i>
            <span class="btn-text">Buy Domain</span>
            <div class="btn-shine"></div>
        </a>
        <a href="{{ route('simple-domains.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Back to Reserve Domains
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-bookmark me-2"></i>
                    {{ $simpleDomain->name }}
                </h5>
            </div>
            <div class="card-body">
                <!-- Domain Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Domain Name</h6>
                        <p class="h5 text-primary">{{ $simpleDomain->name }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Created Date</h6>
                        <p class="mb-0">
                            <i class="fas fa-calendar me-1"></i>
                            {{ $simpleDomain->created_at->format('M d, Y') }}
                            <small class="text-muted">({{ $simpleDomain->created_at->diffForHumans() }})</small>
                        </p>
                    </div>
                </div>

                <!-- Categories -->
                <div class="mb-4">
                    <h6 class="text-muted mb-2">Categories</h6>
                    @if($simpleDomain->category_details->count() > 0)
                        <div class="d-flex flex-wrap gap-2">
                            @foreach($simpleDomain->category_details as $category)
                                <span class="badge fs-6" style="background-color: {{ $category->color }}">
                                    {{ $category->name }}
                                </span>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted mb-0">No categories assigned</p>
                    @endif
                </div>

                <!-- Last Updated -->
                <div class="mb-4">
                    <h6 class="text-muted mb-2">Last Updated</h6>
                    <p class="mb-0">
                        <i class="fas fa-clock me-1"></i>
                        {{ $simpleDomain->updated_at->format('M d, Y \a\t g:i A') }}
                        <small class="text-muted">({{ $simpleDomain->updated_at->diffForHumans() }})</small>
                    </p>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-end gap-2 pt-3 border-top">
                    <a href="{{ route('simple-domains.edit', $simpleDomain) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>
                        Edit Domain
                    </a>
                    <a href="{{ route('simple-domains.buy', $simpleDomain) }}" class="btn btn-buy-domain">
                        <i class="fas fa-shopping-cart me-1"></i>
                        <span class="btn-text">Buy This Domain</span>
                        <div class="btn-shine"></div>
                    </a>
                    <form method="POST" action="{{ route('simple-domains.destroy', $simpleDomain) }}" 
                          class="d-inline" onsubmit="return confirm('Are you sure you want to delete this reserve domain?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>
                            Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Enhanced Buy Button Styling */
.btn-buy-domain {
    position: relative;
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    border: none;
    color: white !important;
    font-weight: 600;
    text-decoration: none !important;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
    border-radius: 8px !important;
    padding: 8px 16px !important;
}

.btn-buy-domain:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.5);
    color: white !important;
}

.btn-buy-domain:active {
    transform: translateY(0);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
}

.btn-buy-domain .btn-text {
    position: relative;
    z-index: 2;
    font-size: 0.875rem;
    font-weight: 600;
}

.btn-buy-domain .fas {
    position: relative;
    z-index: 2;
    font-size: 0.875rem;
}

.btn-buy-domain .btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-buy-domain:hover .btn-shine {
    left: 100%;
}

/* Pulse animation for Buy button */
@keyframes pulse-buy {
    0% {
        box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
    }
    50% {
        box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.6);
    }
    100% {
        box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
    }
}

.btn-buy-domain:focus {
    animation: pulse-buy 1.5s infinite;
    outline: none;
}

@media (max-width: 768px) {
    .btn-buy-domain {
        padding: 6px 12px !important;
        font-size: 0.8rem;
    }

    .btn-buy-domain .btn-text {
        font-size: 0.8rem;
    }

    .btn-buy-domain .fas {
        font-size: 0.8rem;
    }
}
</style>
@endpush