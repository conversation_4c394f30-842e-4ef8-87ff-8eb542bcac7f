import 'package:flutter/material.dart';
import '../models/domain.dart';
import '../models/simple_domain.dart';
import '../services/simple_api_service.dart';

class DomainProvider with ChangeNotifier {
  List<Domain> _domains = [];
  List<SimpleDomain> _simpleDomains = [];
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMoreData = true;
  Map<String, dynamic>? _dashboardStats;

  final SimpleApiService _apiService = SimpleApiService();

  List<Domain> get domains => _domains;
  List<SimpleDomain> get simpleDomains => _simpleDomains;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMoreData => _hasMoreData;
  Map<String, dynamic>? get dashboardStats => _dashboardStats;

  Future<void> loadDomains({
    bool refresh = false,
    String? search,
    int? categoryId,
    int? rating,
    List<String>? extensions,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _domains.clear();
    }

    if (!_hasMoreData || _isLoading) return;

    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.getDomains(
        page: _currentPage,
        search: search,
        categoryId: categoryId,
        rating: rating,
        extensions: extensions,
      );

      if (response['success'] == true) {
        final List<dynamic> domainsData = response['data'];
        final pagination = response['pagination'];

        final newDomains = domainsData
            .map((json) => Domain.fromJson(json))
            .toList();

        if (refresh) {
          _domains = newDomains;
        } else {
          // Avoid duplicates when loading more
          final existingIds = _domains.map((d) => d.id).toSet();
          final uniqueNewDomains = newDomains
              .where((d) => !existingIds.contains(d.id))
              .toList();
          _domains.addAll(uniqueNewDomains);
        }

        _currentPage = pagination['current_page'] + 1;
        _hasMoreData = pagination['current_page'] < pagination['last_page'];
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadSimpleDomains({
    bool refresh = false,
    String? search,
    int? categoryId,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _simpleDomains.clear();
    }

    if (!_hasMoreData || _isLoading) return;

    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.getDomains(
        page: _currentPage,
        search: search,
        categoryId: categoryId,
      );

      if (response['success'] == true) {
        final List<dynamic> domainsData = response['data'];
        final pagination = response['pagination'];

        final newDomains = domainsData
            .map((json) => SimpleDomain.fromJson(json))
            .toList();

        if (refresh) {
          _simpleDomains = newDomains;
        } else {
          _simpleDomains.addAll(newDomains);
        }

        _currentPage = pagination['current_page'] + 1;
        _hasMoreData = pagination['current_page'] < pagination['last_page'];
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createDomain(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      // For now, return true as placeholder - implement when needed
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createSimpleDomain(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      // Placeholder implementation
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> buySimpleDomain(int id, Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      // Placeholder implementation
      await Future.delayed(const Duration(seconds: 1));
      await loadSimpleDomains(refresh: true);
      await loadDomains(refresh: true);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateDomain(int id, Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      // Placeholder implementation
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateSimpleDomain(int id, Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      // Placeholder implementation
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteDomain(int id) async {
    _setLoading(true);
    _clearError();

    try {
      // Placeholder implementation
      await Future.delayed(const Duration(seconds: 1));
      _domains.removeWhere((domain) => domain.id == id);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteSimpleDomain(int id) async {
    _setLoading(true);
    _clearError();

    try {
      // Placeholder implementation
      await Future.delayed(const Duration(seconds: 1));
      _simpleDomains.removeWhere((domain) => domain.id == id);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadDashboardStats() async {
    try {
      final response = await _apiService.getDashboardStats();

      if (response['success'] == true) {
        _dashboardStats = response['data'];
        notifyListeners();
      }
    } catch (e) {
      _setError(e.toString());
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
